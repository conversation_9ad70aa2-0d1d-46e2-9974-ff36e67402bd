* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  overflow: hidden;
  background-color: #0a0a0a;
  color: #ffffff;
}

#app {
  width: 100vw;
  height: 100vh;
  position: relative;
}

#canvas {
  width: 100%;
  height: 100%;
  display: block;
}

#controls {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
  min-width: 300px;
  backdrop-filter: blur(10px);
}

.control-group {
  margin-bottom: 15px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #ddd;
}

.control-group input[type="range"] {
  width: 100%;
  margin-bottom: 5px;
}

.control-group .value {
  font-size: 12px;
  color: #888;
  text-align: right;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

button {
  flex: 1;
  padding: 10px 15px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

button:hover {
  background: #2563eb;
}

button:active {
  transform: scale(0.98);
}

button.secondary {
  background: #6b7280;
}

button.secondary:hover {
  background: #4b5563;
}

.stats-display {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  font-size: 14px;
}

.stats-display h3 {
  margin-bottom: 10px;
  font-size: 16px;
  color: #3b82f6;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-item .label {
  color: #aaa;
}

.stat-item .value {
  color: #fff;
  font-weight: 500;
}

.legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  backdrop-filter: blur(10px);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border-radius: 3px;
}

@media (max-width: 768px) {
  #controls {
    top: 10px;
    right: 10px;
    min-width: 250px;
    padding: 15px;
  }
  
  .legend {
    bottom: 10px;
    left: 10px;
  }
}